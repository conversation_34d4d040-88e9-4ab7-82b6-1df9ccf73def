from fastapi import Fast<PERSON><PERSON>, UploadFile, File
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi import Request
import uvicorn
from services.report_analyzer import ReportAnalyzer
from services.document_parser import DocumentParser
from services.model_service import ModelService
import os
from dotenv import load_dotenv

load_dotenv()

app = FastAPI(title="可研报告评审助手")
# 设置模板目录
current_dir = os.path.dirname(os.path.abspath(__file__))
templates = Jinja2Templates(directory=os.path.join(current_dir, "templates"))
# 初始化服务
model_service = ModelService()
document_parser = DocumentParser()
report_analyzer = ReportAnalyzer(model_service, document_parser)


@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/analyze")
async def analyze_report(pdf_file: UploadFile = File(...)):
    try:
        print(f"收到文件: {pdf_file.filename}")

        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        uploads_dir = os.path.join(current_dir, "uploads")

        # 保存上传的PDF文件
        pdf_path = os.path.join(uploads_dir, pdf_file.filename)
        os.makedirs(uploads_dir, exist_ok=True)
        with open(pdf_path, "wb") as f:
            content = await pdf_file.read()
            f.write(content)

        print(f"文件已保存到: {pdf_path}")

        # 分析报告
        print("开始分析报告...")
        result = report_analyzer.analyze(pdf_path)
        print("分析完成")

        return JSONResponse(content=result)
    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)