import PyPDF2
import re

# 调试 PDF 解析
pdf_path = "uploads/test_可行性研究报告.pdf"

print("调试 PDF 文件内容...")

with open(pdf_path, 'rb') as file:
    reader = PyPDF2.PdfReader(file)
    print(f"PDF 总页数: {len(reader.pages)}")

    # 查看前几页的内容
    for i, page in enumerate(reader.pages[:5]):
        print(f"\n=== 第 {i+1} 页 ===")
        text = page.extract_text()
        lines = text.split('\n')

        print(f"页面行数: {len(lines)}")
        print("前20行内容:")
        for j, line in enumerate(lines[:20]):
            line = line.strip()
            if line:
                print(f"{j+1:2d}: {line}")

        # 检查是否有章节标题
        print("\n可能的章节标题:")
        for line in lines:
            line = line.strip()
            if line and (re.match(r'^\d+[.、\s]', line) or any(keyword in line for keyword in ['概述', '背景', '必要性', '需求分析', '建设方案'])):
                print(f"  -> {line}")
