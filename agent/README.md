# 可研报告评审助手

这是一个基于Python的可研报告评审助手，用于自动评审农村电网巩固提升工程中央预算内投资项目的可行性研究报告。

## 功能特点

- 支持PDF格式可研报告的自动解析
- 基于《农村电网巩固提升工程中央预算内投资项目可行性研究报告编制和审查指南》进行评审
- 使用大模型进行智能分析和评审
- 提供详细的评审结果和修改建议

## 安装说明

1. 克隆项目到本地
2. 安装依赖：
```bash
pip install -r requirements.txt
```
3. 复制`.env.example`为`.env`并配置相关参数：
```bash
cp .env.example .env
```

## 使用方法

1. 启动服务：
```bash
python main.py
```

2. 访问API接口：
- 接口地址：`http://localhost:8000/analyze`
- 方法：POST
- 参数：PDF文件（multipart/form-data）

## 项目结构

```
.
├── main.py              # 主程序入口
├── requirements.txt     # 项目依赖
├── services/           # 服务层
│   ├── document_parser.py  # 文档解析服务
│   ├── model_service.py    # 模型服务
│   └── report_analyzer.py  # 报告分析服务
└── templates/          # 模板文件
    ├── outline.docx    # 大纲文档
    └── review_criteria.xlsx  # 审查细则
```

## 注意事项

1. 使用前请确保已正确配置大模型API密钥
2. 需要准备大纲文档和审查细则文件
3. 建议使用Python 3.8及以上版本 