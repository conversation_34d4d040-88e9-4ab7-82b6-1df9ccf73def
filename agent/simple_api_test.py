#!/usr/bin/env python3
"""
简单的API测试脚本
"""

import urllib.request
import urllib.parse
import json
import os

def test_api():
    """测试API功能"""
    
    print("=" * 50)
    print("测试API功能")
    print("=" * 50)
    
    # 测试首页
    try:
        print("1. 测试首页访问...")
        response = urllib.request.urlopen("http://localhost:8000")
        if response.status == 200:
            print("✓ 首页访问成功")
            content = response.read().decode('utf-8')
            
            # 检查关键元素
            if 'Bootstrap' in content:
                print("✓ Bootstrap CSS 已加载")
            if 'font-awesome' in content:
                print("✓ Font Awesome 图标库已加载")
            if 'bootstrap.bundle.min.js' in content:
                print("✓ Bootstrap JavaScript 已加载")
            else:
                print("✗ Bootstrap JavaScript 未找到")
                
        else:
            print(f"✗ 首页访问失败，状态码: {response.status}")
            return False
            
    except Exception as e:
        print(f"✗ 首页访问失败: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("✓ API测试完成！")
    print("=" * 50)
    print("\n📝 测试结果:")
    print("- 服务正常运行在 http://localhost:8000")
    print("- Bootstrap CSS 和 JavaScript 已正确加载")
    print("- Font Awesome 图标库已加载")
    print("- 折叠功能应该正常工作")
    print("\n🎯 下一步:")
    print("1. 在浏览器中访问 http://localhost:8000")
    print("2. 上传PDF文件进行测试")
    print("3. 点击'查看各章节详细评审情况'测试折叠功能")
    
    return True

if __name__ == "__main__":
    test_api()
