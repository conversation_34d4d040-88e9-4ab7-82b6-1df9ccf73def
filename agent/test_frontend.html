<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端显示测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .criterion-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .criterion-header {
            background-color: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
        }
        .criterion-content {
            padding: 15px;
        }
        .result-badge {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
        .result-符合 { background-color: #198754; color: white; }
        .result-基本符合 { background-color: #fd7e14; color: white; }
        .result-不符合 { background-color: #dc3545; color: white; }
        .result-不适用 { background-color: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h2>评审结果汇总优化 - 前端显示测试</h2>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> 测试说明</h5>
            <p>此页面展示优化后的评审结果显示效果，包括：</p>
            <ul>
                <li>全文综合分析</li>
                <li>关键发现</li>
                <li>改进建议</li>
                <li>各章节详细评审情况（可折叠）</li>
            </ul>
        </div>

        <div id="criteriaList">
            <!-- 审查细则将在这里动态生成 -->
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟测试数据
        const testData = {
            "criterion_id": "1.1",
            "criterion_content": "35千伏及以上电压等级项目以单个项目、35千伏以下电压等级项目以县域为单位编制项目可行性研究报告。",
            "overall_result": "符合",
            "overall_assessment": "基本符合",
            "comprehensive_analysis": "经过对报告全文的综合分析，该项目在电压等级分类和可研报告编制方面基本符合要求。报告明确了项目为35千伏以下电压等级项目，并按县域为单位进行编制，符合相关规定。但在某些技术细节描述上还有进一步完善的空间。",
            "key_findings": [
                "项目明确属于35千伏以下电压等级，符合县域编制要求",
                "可研报告结构完整，编制单位具备相应资质",
                "技术方案描述较为详细，但部分参数需要进一步核实"
            ],
            "recommendations": [
                "建议在技术方案章节中补充更详细的电压等级分析",
                "完善项目建设规模与县域电网规划的衔接说明",
                "建议增加与相关标准规范的对比分析"
            ],
            "section_results": [
                {
                    "section": "1 概述",
                    "result": "符合",
                    "explanation": "概述章节明确了项目的电压等级和编制依据"
                },
                {
                    "section": "2 项目建设背景和必要性",
                    "result": "基本符合",
                    "explanation": "背景描述较为充分，但与县域规划的关联性需要加强"
                },
                {
                    "section": "5 项目建设方案",
                    "result": "符合",
                    "explanation": "建设方案技术路线清晰，符合电压等级要求"
                }
            ]
        };

        // 渲染测试数据
        function renderTestCriterion() {
            const criteriaList = document.getElementById('criteriaList');
            const criterion = testData;
            
            const criterionCard = document.createElement('div');
            criterionCard.className = 'criterion-card';

            // 综合分析内容
            let comprehensiveAnalysis = '';
            if (criterion.comprehensive_analysis) {
                comprehensiveAnalysis = `
                    <div class="alert alert-info mb-3">
                        <h6><i class="fas fa-chart-line"></i> 全文综合分析：</h6>
                        <p class="mb-0">${criterion.comprehensive_analysis}</p>
                    </div>
                `;
            }

            // 关键发现
            let keyFindings = '';
            if (criterion.key_findings && criterion.key_findings.length > 0) {
                keyFindings = `
                    <div class="mb-3">
                        <strong><i class="fas fa-search"></i> 关键发现：</strong>
                        <ul class="list-unstyled mt-2">
                            ${criterion.key_findings.map(finding => `
                                <li class="mb-1"><i class="fas fa-dot-circle text-primary"></i> ${finding}</li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }

            // 改进建议
            let suggestions = '';
            const recommendations = criterion.recommendations || [];
            if (recommendations.length > 0) {
                suggestions = `
                    <div class="mb-3">
                        <strong><i class="fas fa-lightbulb"></i> 改进建议：</strong>
                        <ul class="list-unstyled mt-2">
                            ${recommendations.map(suggestion => `
                                <li class="mb-1"><i class="fas fa-arrow-right text-success"></i> ${suggestion}</li>
                            `).join('')}
                        </ul>
                    </div>
                `;
            }

            // 章节详情（折叠显示）
            let sectionDetails = '';
            if (criterion.section_results && criterion.section_results.length > 0) {
                const relevantSections = criterion.section_results.filter(s => s.result !== '不适用');
                if (relevantSections.length > 0) {
                    const collapseId = `collapse-${criterion.criterion_id.replace(/\./g, '-')}`;
                    sectionDetails = `
                        <div class="mt-3">
                            <p>
                                <a class="btn btn-outline-secondary btn-sm" data-bs-toggle="collapse" href="#${collapseId}" role="button" aria-expanded="false">
                                    <i class="fas fa-list"></i> 查看各章节详细评审情况 (${relevantSections.length}个相关章节)
                                </a>
                            </p>
                            <div class="collapse" id="${collapseId}">
                                <div class="card card-body">
                                    <ul class="list-unstyled">
                                        ${relevantSections.map(section => `
                                            <li class="mb-2">
                                                <span class="badge result-badge result-${section.result}">${section.result}</span>
                                                <strong>${section.section}</strong>
                                                ${section.explanation ? `<br><small class="text-muted">${section.explanation}</small>` : ''}
                                            </li>
                                        `).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }

            // 确定显示的评审结果
            const displayResult = criterion.overall_assessment || criterion.overall_result || '未知';

            criterionCard.innerHTML = `
                <div class="criterion-header">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="mb-1">审查细则 ${criterion.criterion_id}</h6>
                            <p class="mb-0 text-muted">${criterion.criterion_content}</p>
                        </div>
                        <span class="badge result-badge result-${displayResult}">${displayResult}</span>
                    </div>
                </div>
                <div class="criterion-content">
                    ${comprehensiveAnalysis}
                    ${keyFindings}
                    ${suggestions}
                    ${sectionDetails}
                </div>
            `;

            criteriaList.appendChild(criterionCard);
        }

        // 页面加载完成后渲染测试数据
        document.addEventListener('DOMContentLoaded', function() {
            renderTestCriterion();
        });
    </script>
</body>
</html>
