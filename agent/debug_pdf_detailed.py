from services.document_parser import DocumentParser
import PyPDF2
import re

# 详细调试 PDF 解析
pdf_path = "../docs/24年部分可研报告样本和评审结果/1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告.pdf"

print("=== 详细调试 PDF 解析 ===")

# 1. 先直接提取PDF文本
print("\n1. 提取PDF原始文本...")
with open(pdf_path, 'rb') as file:
    reader = PyPDF2.PdfReader(file)
    all_text = ""
    for page in reader.pages:
        try:
            text = page.extract_text()
            if text.strip():
                all_text += text + "\n"
        except Exception as e:
            print(f"页面文本提取失败: {e}")

lines = all_text.split('\n')
print(f"总行数: {len(lines)}")

# 2. 查找可能的章节标题
print("\n2. 查找可能的章节标题...")
parser = DocumentParser()
potential_chapters = []

for i, line in enumerate(lines):
    line_clean = line.strip()
    if not line_clean:
        continue
    
    # 检查是否匹配章节模式
    is_title, chapter_title = parser.is_chapter_title(line_clean)
    if is_title:
        potential_chapters.append((i+1, line_clean, chapter_title))
    
    # 也检查包含标准章节名称的行
    for num, title in parser.standard_outline.items():
        if title in line_clean and len(line_clean) < 100:
            potential_chapters.append((i+1, line_clean, f"可能的{num} {title}"))

print(f"找到 {len(potential_chapters)} 个潜在章节标题:")
for line_num, original, matched in potential_chapters[:20]:  # 只显示前20个
    print(f"  第{line_num}行: {original} -> {matched}")

# 3. 检测目录区域
print("\n3. 检测目录区域...")
toc_start, toc_end = parser.detect_toc_region(lines)
if toc_start >= 0 and toc_end >= 0:
    print(f"目录区域: 第{toc_start+1}行到第{toc_end}行")
    print("目录内容预览:")
    for i in range(toc_start, min(toc_end + 1, toc_start + 10)):
        print(f"  第{i+1}行: {lines[i].strip()}")
else:
    print("未检测到目录区域")

# 4. 分析文本内容分布
print("\n4. 分析文本内容分布...")
content_stats = {}
keywords_map = {
    "概述": ["概述", "项目概况", "总体情况", "基本情况"],
    "建设背景": ["建设背景", "必要性", "项目背景", "建设必要性"],
    "需求分析": ["需求分析", "预期产出", "需求", "产出"],
    "选址": ["选址", "要素保障", "建设条件", "选址条件"],
    "建设方案": ["建设方案", "技术方案", "工程方案", "实施方案"],
    "运营方案": ["运营方案", "运营管理", "运行方案", "管理方案"],
    "投融资": ["投融资", "财务方案", "投资估算", "资金筹措"],
    "影响效果": ["影响效果", "效益分析", "社会效益", "经济效益"],
    "风险管控": ["风险管控", "风险分析", "风险防范", "风险评估"],
    "结论建议": ["研究结论", "建议", "结论", "总结"],
    "附表": ["附表", "附件", "附录", "表格"]
}

for category, keywords in keywords_map.items():
    count = 0
    for line in lines:
        for keyword in keywords:
            if keyword in line:
                count += 1
                break
    content_stats[category] = count

print("关键词分布统计:")
for category, count in content_stats.items():
    print(f"  {category}: {count} 行")

# 5. 测试优化后的解析器
print("\n5. 测试优化后的解析器...")
sections = parser.parse_pdf(pdf_path)

print("解析结果:")
for section_title, content in sections.items():
    print(f"  {section_title}: {len(content)} 字符")
    if content.strip():
        # 显示前100个字符
        preview = content.strip()[:100].replace('\n', ' ')
        print(f"    预览: {preview}...")

print("\n=== 调试完成 ===")
