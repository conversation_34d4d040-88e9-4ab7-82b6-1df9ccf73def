from typing import Dict, Any, List
from .document_parser import DocumentParser
from .model_service import ModelService
import os

class ReportAnalyzer:
    def __init__(self, model_service: ModelService, document_parser: DocumentParser):
        self.model_service = model_service
        self.document_parser = document_parser
        self.outline = None
        self.criteria = None

        # 获取当前文件所在目录的父目录（agent目录）
        self.current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    def _load_templates(self):
        """延迟加载模板文件"""
        if self.outline is None or self.criteria is None:
            print("加载模板文件...")

            # 解析大纲和审查细则
            outline_path = os.path.join(self.current_dir, 
                                os.getenv("OUTLINE_FILE", "templates/可行性研究报告编制和审查指南.docx"))
            criteria_path = os.path.join(self.current_dir, 
                                os.getenv("RULES_FILE", "templates/中央预算投资项目审核表.xlsx"))

            print(f"大纲文件路径: {outline_path}")
            print(f"审查细则文件路径: {criteria_path}")

            self.outline = self.document_parser.parse_outline(outline_path)
            self.criteria = self.document_parser.parse_review_criteria(criteria_path)

            print(f"加载完成: 大纲 {len(self.outline)} 章节, 审查细则 {len(self.criteria)} 项")

    def analyze(self, pdf_path: str) -> Dict[str, Any]:
        """分析可研报告"""
        # 确保模板已加载
        self._load_templates()

        # 解析PDF文件
        print(f"开始解析PDF文件: {pdf_path}")
        sections = self.document_parser.parse_pdf(pdf_path)
        print(f"PDF解析完成，提取到 {len(sections)} 个章节")

        # 对每个章节进行批量分析
        review_results = []
        for section_title, section_content in sections.items():
            print(f"\n开始分析章节: {section_title}")

            # 使用批量分析方法，一次性分析该章节对所有审查细则的符合情况
            batch_result = self.model_service.analyze_section_batch(
                section_title=section_title,
                section_content=section_content,
                all_criteria=self.criteria,
                outline=str(self.outline)
            )

            # 构建章节评审结果
            section_review = {
                "section": section_title,
                "content_length": len(section_content),
                "has_content": bool(section_content.strip()),
                "analysis": []
            }

            # 处理批量分析结果
            if "criteria_results" in batch_result:
                for criterion_result in batch_result["criteria_results"]:
                    section_review["analysis"].append({
                        "criterion_id": criterion_result.get("criterion_id", "unknown"),
                        "criterion_content": criterion_result.get("criterion_content", ""),
                        "result": criterion_result.get("result", "不适用"),
                        "explanation": criterion_result.get("explanation", "")
                    })
            else:
                # 如果批量分析失败，回退到原有方法
                print(f"批量分析失败，回退到逐个分析模式")
                return {"error": "批量分析失败"}

            review_results.append(section_review)
            print(f"章节 {section_title} 分析完成，共评审 {len(section_review['analysis'])} 个审查细则")

        # 按审查细则重新组织结果
        print(f"\n开始按审查细则组织评审结果...")
        criteria_results = self._organize_by_criteria(review_results)

        # 对每个审查细则进行全文综合分析
        print(f"\n开始进行全文综合分析...")
        comprehensive_analysis = self.model_service.analyze_criteria_comprehensive(
            criteria_results, sections
        )
        enhanced_criteria = comprehensive_analysis.get("enhanced_criteria", criteria_results)

        # 生成总体评审意见
        print(f"\n开始生成总体评审意见...")
        summary = self.model_service.summarize_review(review_results)

        return {
            "criteria_analysis": enhanced_criteria,
            "sections": review_results,  # 保留原有章节结构供参考
            "summary": summary["summary"],
            "statistics": self._generate_statistics(review_results)
        }

    def _generate_statistics(self, review_results: list) -> Dict[str, Any]:
        """生成评审统计信息"""
        total_criteria = 0
        result_counts = {"符合": 0, "基本符合": 0, "不符合": 0, "不适用": 0}

        for section in review_results:
            for analysis in section["analysis"]:
                total_criteria += 1
                result = analysis.get("result", "不适用")
                if result in result_counts:
                    result_counts[result] += 1
                else:
                    result_counts["不适用"] += 1

        return {
            "total_criteria": total_criteria,
            "total_sections": len(review_results),
            "result_distribution": result_counts,
            "compliance_rate": round((result_counts["符合"] + result_counts["基本符合"]) / max(total_criteria, 1) * 100, 2) if total_criteria > 0 else 0
        }

    def _organize_by_criteria(self, review_results: list) -> List[Dict[str, Any]]:
        """按审查细则重新组织评审结果"""
        criteria_map = {}

        # 遍历所有章节的评审结果，按审查细则ID分组
        for section in review_results:
            section_name = section.get("section", "未知章节")
            section_has_content = section.get("has_content", False)

            for analysis in section.get("analysis", []):
                criterion_id = analysis.get("criterion_id", "unknown")
                criterion_content = analysis.get("criterion_content", "")
                result = analysis.get("result", "不适用")
                explanation = analysis.get("explanation", "")

                # 跳过无效ID的审查细则（但保留行号生成的ID）
                if criterion_id in ["unknown", "parse_error", "api_error"]:
                    print(f"跳过无效的审查细则ID: '{criterion_id}'")
                    continue

                # 如果这个审查细则还没有记录，创建新记录
                if criterion_id not in criteria_map:
                    criteria_map[criterion_id] = {
                        "criterion_id": criterion_id,
                        "criterion_content": criterion_content,
                        "section_results": [],
                        "overall_result": "不适用",
                        "compliance_sections": [],
                        "non_compliance_sections": [],
                        "not_applicable_sections": [],
                        "improvement_suggestions": []
                    }

                # 检查是否已经存在相同的章节结果（去重）
                existing_sections = [sr["section"] for sr in criteria_map[criterion_id]["section_results"]]
                if section_name in existing_sections:
                    print(f"跳过重复的章节结果: 审查细则{criterion_id} - {section_name}")
                    continue

                # 添加该章节对此审查细则的评审结果
                criteria_map[criterion_id]["section_results"].append({
                    "section": section_name,
                    "has_content": section_has_content,
                    "result": result,
                    "explanation": explanation
                })

                # 根据结果分类章节（也需要去重）
                if result == "符合":
                    if not any(cs["section"] == section_name for cs in criteria_map[criterion_id]["compliance_sections"]):
                        criteria_map[criterion_id]["compliance_sections"].append({
                            "section": section_name,
                            "explanation": explanation
                        })
                elif result == "基本符合":
                    if not any(cs["section"] == section_name for cs in criteria_map[criterion_id]["compliance_sections"]):
                        criteria_map[criterion_id]["compliance_sections"].append({
                            "section": section_name,
                            "explanation": explanation
                        })
                elif result == "不符合":
                    if not any(ncs["section"] == section_name for ncs in criteria_map[criterion_id]["non_compliance_sections"]):
                        criteria_map[criterion_id]["non_compliance_sections"].append({
                            "section": section_name,
                            "explanation": explanation
                        })
                else:  # 不适用
                    if not any(nas["section"] == section_name for nas in criteria_map[criterion_id]["not_applicable_sections"]):
                        criteria_map[criterion_id]["not_applicable_sections"].append({
                            "section": section_name,
                            "explanation": explanation
                        })

        # 为每个审查细则确定总体结果和改进建议
        for criterion_id, criterion_data in criteria_map.items():
            criterion_data["overall_result"] = self._determine_overall_result(criterion_data)
            criterion_data["improvement_suggestions"] = self._generate_improvement_suggestions(criterion_data)

        # 按审查细则ID排序并返回
        sorted_criteria = sorted(criteria_map.values(), key=lambda x: self._sort_criterion_id(x["criterion_id"]))
        return sorted_criteria

    def _determine_overall_result(self, criterion_data: Dict[str, Any]) -> str:
        """确定审查细则的总体评审结果"""
        compliance_count = len(criterion_data["compliance_sections"])
        non_compliance_count = len(criterion_data["non_compliance_sections"])

        # 如果有不符合的章节，总体结果为不符合
        if non_compliance_count > 0:
            return "不符合"
        # 如果有符合的章节，总体结果为符合或基本符合
        elif compliance_count > 0:
            # 检查是否所有有内容的章节都符合
            total_content_sections = sum(1 for result in criterion_data["section_results"]
                                       if result["has_content"] and result["result"] != "不适用")
            if total_content_sections > 0 and compliance_count == total_content_sections:
                return "符合"
            else:
                return "基本符合"
        # 如果所有章节都不适用
        else:
            return "不适用"

    def _generate_improvement_suggestions(self, criterion_data: Dict[str, Any]) -> List[str]:
        """为审查细则生成改进建议"""
        suggestions = []

        # 针对不符合的章节生成建议
        for non_compliance in criterion_data["non_compliance_sections"]:
            if non_compliance["explanation"] and non_compliance["explanation"] != "不适用":
                suggestions.append(f"在{non_compliance['section']}中：{non_compliance['explanation']}")

        # 如果没有具体的不符合项，但总体结果不是完全符合，给出通用建议
        if not suggestions and criterion_data["overall_result"] in ["不符合", "基本符合"]:
            suggestions.append(f"建议在相关章节中补充完善{criterion_data['criterion_content'][:50]}...的相关内容")

        return suggestions

    def _sort_criterion_id(self, criterion_id: str) -> tuple:
        """审查细则ID排序辅助函数"""
        try:
            # 尝试提取数字部分进行排序
            if criterion_id and criterion_id.replace(".", "").isdigit():
                return (float(criterion_id), "")
            else:
                return (999, criterion_id)  # 非数字ID排在后面
        except:
            return (999, criterion_id)