import os
import re
from openai import OpenAI
from typing import Dict, Any

class ModelService:
    def __init__(self):
        self.client = OpenAI(
            api_key=os.getenv("OPENAI_API_KEY"),
            base_url=os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
        )

    def _clean_response(self, content: str) -> str:
        """清理推理模型的响应内容，去除思考标签"""
        # 去除 <think>...</think> 标签及其内容
        content = re.sub(r'<think>.*?</think>', '', content, flags=re.DOTALL)

        # 去除 <thinking>...</thinking> 标签及其内容
        content = re.sub(r'<thinking>.*?</thinking>', '', content, flags=re.DOTALL)

        # 去除多余的空行
        content = re.sub(r'\n\s*\n', '\n\n', content)

        return content.strip()

    def analyze_section(self, section_content: str, review_criteria: str, outline:str = None) -> Dict[str, Any]:
        """分析单个章节内容"""
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            return {
                "analysis": f"[测试模式] 对章节内容的分析结果：\n审查细则：{review_criteria[:50]}...\n章节内容长度：{len(section_content)} 字符\n评审结果：基本符合要求"
            }

        # 优化的提示词，更聚焦和简洁
        content = f"""对章节内容：{section_content} 进行评审分析
"""

        try:
            print(f"正在调用大模型分析章节内容...")
            print(f"审查细则: {review_criteria[:100]}...")

            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": f"""
# 角色
你是专业的可研报告评审专家，可以按照《可研报告编制大纲》（见下文），及《审查细则》（见下文）对用户提供的部分章节内容进行评审。
# 职责
根据用户输入的可研报告的部分章节，对比可研报告大纲和审查细则，输出审查情况：（符合、基本符合、不符合），不符合情况给出具体原因，如：改动了大纲8.1、缺少大纲7.4、未提供可研汇总表等。
# 工作流程
首先要检查该章节内容是否与审查细则相关，如相关则并给出审查情况，如果不相关可输出不适用。以第1章节为例，对于"审查细则1"可能输出内容包括：
  - 当与章节1与"审查项1"不相关时，可输出：不适用该审查项
  - 当与章节1与"审查项1"相关时，可输出：具体的审查情况，如
    * 或 章节1 基本符合
    * 或 章节1 改动了大纲标题：1.2
    * 或 章节1 投资估算编制说明应包括工程量确定的主要依据和计算原则
  - 检查章节内容时，也需要对比大纲中相应章节的要求。
# 输出规范
请直接给出评审结果，格式如下：
评审结果：[符合/基本符合/不符合/不适用]
具体说明：[如果不符合，请说明具体原因；如果不适用，请说明原因]
 - 1. 只针对该审查细则进行评审，不要扩展到其他内容
 - 2. 如果章节内容与审查细则无关，请回答"不适用"
 - 3. 回答要简洁明确，不要冗长的解释
# 可研报告大纲内容：
 {outline}


# 审查细则
{review_criteria}

"""},
                    {"role": "user", "content": content}
                ],
                timeout=120  # 设置120秒超时
            )

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            print(f"\n大模型响应: {cleaned_content[:200]}...\n")

            return {
                "analysis": cleaned_content
            }
        except Exception as e:
            print(f"API调用失败: {e}")
            return {
                "analysis": f"[API调用失败] 无法完成分析：{str(e)}"
            }

    def summarize_review(self, review_results: list) -> Dict[str, Any]:
        """汇总所有评审结果"""
        # 如果是测试模式，返回模拟结果
        if os.getenv("TEST_MODE") == "true":
            return {
                "summary": f"[测试模式] 总体评审意见：\n共分析了 {len(review_results)} 个章节\n总体评审结论：基本符合要求\n主要问题：无重大问题\n改进建议：建议进一步完善细节"
            }

        # 优化的提示词，更聚焦
        prompt = f"""你是专业的可研报告评审专家，请根据各章节评审结果给出总体评审意见。

各章节评审结果：
{str(review_results)}

请按以下格式给出总体评审意见：
总体评审结论：[符合/基本符合/不符合]
主要问题汇总：[列出主要不符合项]
改进建议：[针对问题提出具体改进建议]

注意：
1. 基于实际评审结果进行总结，不要添加未提及的内容
2. 重点关注不符合和基本符合的项目
3. 建议要具体可操作"""

        try:
            print(f"正在调用大模型汇总评审结果...")
            print(f"共有 {len(review_results)} 个章节的评审结果")

            response = self.client.chat.completions.create(
                model=os.getenv("MODEL_NAME", "qwq-32b"),
                messages=[
                    {"role": "system", "content": "你是一个专业的可研报告评审专家，请严格按照要求格式回答。"},
                    {"role": "user", "content": prompt}
                ],
                timeout=120  # 设置120秒超时
            )

            # 清理响应内容
            raw_content = response.choices[0].message.content
            cleaned_content = self._clean_response(raw_content)

            print(f"\n大模型汇总响应: {cleaned_content[:200]}...\n")

            return {
                "summary": cleaned_content
            }
        except Exception as e:
            print(f"API调用失败: {e}")
            return {
                "summary": f"[API调用失败] 无法完成总结：{str(e)}"
            }