#!/usr/bin/env python3
import os
print("开始测试...")

print("1. 测试基本导入...")
try:
    import PyPDF2
    print("✓ PyPDF2 导入成功")
except Exception as e:
    print(f"✗ PyPDF2 导入失败: {e}")

try:
    from docx import Document
    print("✓ python-docx 导入成功")
except Exception as e:
    print(f"✗ python-docx 导入失败: {e}")

try:
    import pandas as pd
    print("✓ pandas 导入成功")
except Exception as e:
    print(f"✗ pandas 导入失败: {e}")

try:
    from openai import OpenAI
    print("✓ openai 导入成功")
except Exception as e:
    print(f"✗ openai 导入失败: {e}")

print("\n2. 测试文件路径...")
current_dir = os.path.dirname(os.path.abspath(__file__))
print(f"当前目录: {current_dir}")

outline_path = os.path.join(current_dir, "templates", "可行性研究报告编制和审查指南.docx")
print(f"大纲文件: {outline_path}")
print(f"大纲文件存在: {os.path.exists(outline_path)}")

criteria_path = os.path.join(current_dir, "templates", "中央预算投资项目审核表.xlsx")
print(f"审查细则文件: {criteria_path}")
print(f"审查细则文件存在: {os.path.exists(criteria_path)}")

print("\n3. 测试环境变量...")
from dotenv import load_dotenv
load_dotenv()

api_key = os.getenv("OPENAI_API_KEY")
api_base = os.getenv("OPENAI_API_BASE")
model_name = os.getenv("MODEL_NAME")

print(f"API Key: {api_key[:10]}..." if api_key else "API Key: None")
print(f"API Base: {api_base}")
print(f"Model Name: {model_name}")

print("\n4. 测试文档解析...")
try:
    if os.path.exists(outline_path):
        doc = Document(outline_path)
        print(f"✓ Word文档打开成功，包含 {len(doc.paragraphs)} 个段落")
    else:
        print("✗ Word文档不存在")
except Exception as e:
    print(f"✗ Word文档解析失败: {e}")

print("\n测试完成")
