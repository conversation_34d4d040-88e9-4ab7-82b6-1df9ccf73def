import json

# 显示所有审查细则的详细信息
try:
    with open('test_result.json', 'r', encoding='utf-8') as f:
        result = json.load(f)
    
    print("=== 所有审查细则的评审结果 ===")
    
    if 'criteria_analysis' in result:
        criteria_analysis = result['criteria_analysis']
        
        for i, criterion in enumerate(criteria_analysis):
            print(f"\n{'='*60}")
            print(f"审查细则 {i+1}: {criterion.get('criterion_id', 'N/A')}")
            print(f"{'='*60}")
            print(f"细则内容: {criterion.get('criterion_content', 'N/A')}")
            print(f"总体结果: {criterion.get('overall_result', 'N/A')}")
            
            # 显示各章节的评审结果
            section_results = criterion.get('section_results', [])
            print(f"\n各章节评审结果 (共{len(section_results)}个章节):")
            for section_result in section_results:
                section_name = section_result.get('section', 'N/A')
                has_content = section_result.get('has_content', False)
                result_status = section_result.get('result', 'N/A')
                explanation = section_result.get('explanation', 'N/A')
                
                content_indicator = "✓" if has_content else "✗"
                print(f"  [{content_indicator}] {section_name}: {result_status}")
                if explanation and explanation != 'N/A':
                    print(f"      说明: {explanation[:100]}...")
            
            # 显示改进建议
            suggestions = criterion.get('improvement_suggestions', [])
            if suggestions:
                print(f"\n改进建议:")
                for j, suggestion in enumerate(suggestions, 1):
                    print(f"  {j}. {suggestion}")
            else:
                print(f"\n改进建议: 无")
    
    print(f"\n{'='*60}")
    print("评审完成")
    print(f"{'='*60}")
        
except Exception as e:
    print(f"解析失败: {e}")
    import traceback
    traceback.print_exc()
