#!/usr/bin/env python3
"""
测试评审结果汇总优化功能
"""

import os
import sys
import json
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 设置测试模式
os.environ["TEST_MODE"] = "true"

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.report_analyzer import ReportAnalyzer
from services.document_parser import DocumentParser
from services.model_service import ModelService

def test_optimization():
    """测试优化后的评审结果汇总功能"""
    print("=" * 60)
    print("测试评审结果汇总优化功能")
    print("=" * 60)
    
    # 初始化服务
    model_service = ModelService()
    document_parser = DocumentParser()
    report_analyzer = ReportAnalyzer(model_service, document_parser)
    
    # 使用现有的PDF文件进行测试
    pdf_path = "uploads/1.广西电网有限责任公司2025年农村电网巩固提升工程中央预算内投资计划需求项目（横州市）可行性研究报告.pdf"
    
    if not os.path.exists(pdf_path):
        print(f"错误：测试文件不存在 {pdf_path}")
        return False
    
    try:
        print(f"开始分析PDF文件: {pdf_path}")
        result = report_analyzer.analyze(pdf_path)
        
        print("\n" + "=" * 40)
        print("分析结果结构验证")
        print("=" * 40)
        
        # 验证结果结构
        required_keys = ["criteria_analysis", "sections", "summary", "statistics"]
        for key in required_keys:
            if key in result:
                print(f"✓ {key}: 存在")
            else:
                print(f"✗ {key}: 缺失")
                return False
        
        # 验证criteria_analysis的新字段
        print("\n" + "=" * 40)
        print("审查细则分析结果验证")
        print("=" * 40)
        
        criteria_analysis = result.get("criteria_analysis", [])
        print(f"审查细则总数: {len(criteria_analysis)}")
        
        if criteria_analysis:
            # 检查第一个审查细则的新字段
            first_criterion = criteria_analysis[0]
            print(f"\n第一个审查细则 ({first_criterion.get('criterion_id', 'unknown')}) 的字段:")
            
            # 检查新增的字段
            new_fields = [
                "comprehensive_analysis",
                "overall_assessment", 
                "key_findings",
                "recommendations"
            ]
            
            for field in new_fields:
                if field in first_criterion:
                    value = first_criterion[field]
                    if isinstance(value, list):
                        print(f"✓ {field}: {len(value)} 项")
                        if value:
                            print(f"  示例: {value[0][:50]}...")
                    else:
                        print(f"✓ {field}: {str(value)[:50]}...")
                else:
                    print(f"✗ {field}: 缺失")
            
            # 检查原有字段是否保留
            original_fields = [
                "criterion_id",
                "criterion_content", 
                "section_results",
                "overall_result"
            ]
            
            print(f"\n原有字段保留情况:")
            for field in original_fields:
                if field in first_criterion:
                    print(f"✓ {field}: 保留")
                else:
                    print(f"✗ {field}: 缺失")
        
        # 验证统计信息
        print("\n" + "=" * 40)
        print("统计信息验证")
        print("=" * 40)
        
        statistics = result.get("statistics", {})
        stat_fields = ["total_criteria", "total_sections", "result_distribution", "compliance_rate"]
        for field in stat_fields:
            if field in statistics:
                print(f"✓ {field}: {statistics[field]}")
            else:
                print(f"✗ {field}: 缺失")
        
        # 保存测试结果
        output_file = "test_optimization_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n测试结果已保存到: {output_file}")
        
        print("\n" + "=" * 60)
        print("✓ 优化功能测试完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_optimization()
    sys.exit(0 if success else 1)
