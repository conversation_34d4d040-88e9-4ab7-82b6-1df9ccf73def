from services.document_parser import DocumentParser
import os

def test_pdf_file(pdf_path, file_name):
    """测试单个PDF文件"""
    print(f"\n{'='*60}")
    print(f"测试文件: {file_name}")
    print(f"文件路径: {pdf_path}")
    print(f"{'='*60}")

    parser = DocumentParser()

    try:
        sections = parser.parse_pdf(pdf_path)
        print(f"解析成功，提取到 {len(sections)} 个章节")

        for section_title, content in sections.items():
            print(f"\n章节: {section_title}")
            print(f"内容长度: {len(content)} 字符")
            if content.strip():
                print(f"内容预览: {content[:200]}...")
            else:
                print("内容为空")

        # 统计有内容的章节数量
        non_empty_sections = sum(1 for content in sections.values() if content.strip())
        print(f"\n总结: 共{len(sections)}个章节，其中{non_empty_sections}个有内容")

        # 显示章节内容统计
        print("\n章节内容统计:")
        for section_title, content in sections.items():
            status = "✓ 有内容" if content.strip() else "✗ 无内容"
            char_count = len(content) if content.strip() else 0
            print(f"  {section_title}: {status} ({char_count}字符)")

        return sections

    except Exception as e:
        print(f"PDF 解析失败: {e}")
        import traceback
        traceback.print_exc()
        return None

# 测试两个PDF文件
pdf_files = [
    #("uploads/1.广西电网横州市可行性研究报告.pdf", "广西电网横州市可行性研究报告"),
    ("uploads/test_可行性研究报告.pdf", "test_可行性研究报告")
]

for pdf_path, file_name in pdf_files:
    if os.path.exists(pdf_path):
        test_pdf_file(pdf_path, file_name)
    else:
        print(f"\n文件不存在: {pdf_path}")

